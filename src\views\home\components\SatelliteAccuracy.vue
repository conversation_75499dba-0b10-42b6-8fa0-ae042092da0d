<template>
  <div class="satellite-accuracy">
    <div class="chart-section">
      <div class="chart-header">
        <div class="header-main">
          <div class="title-wrapper">
            <i class="el-icon-data-analysis" />
            <span class="chart-title">系统授时精度</span>
          </div>
          <!-- <div class="time-control">
            <el-button
              type="text"
              :disabled="!canGoPrev"
              @click="handlePrevHour"
            >
              <el-icon><ArrowLeft /></el-icon>
            </el-button>
            <span class="time-range">{{ currentTimeRange }}</span>
            <el-button
              type="text"
              :disabled="!canGoNext"
              @click="handleNextHour"
            >
              <el-icon><ArrowRight /></el-icon>
            </el-button>
          </div> -->
        </div>
        <div class="legend-info">
          <div class="info-title">授时精度等级说明：</div>
          <div class="info-content">
            <div
              class="info-item"
              v-for="(range, level) in accuracyLevels"
              :key="level"
            >
              <span class="level">{{ level }}级：</span>
              <span class="range">{{ range }}</span>
            </div>
          </div>
        </div>
        <div class="title-divider"></div>
      </div>
      <div class="chart-content">
        <bar-chart :data="lineChartOption" ref="accuracyChartRef" />
      </div>
    </div>
    <div class="table-section">
      <div class="table-header">
        <div class="title-wrapper">
          <i class="el-icon-time" />
          <span class="table-title">单星授时精度</span>
        </div>
        <div class="title-divider"></div>
      </div>
      <el-table :data="accuracyList" border style="width: 100%" height="230">
        <el-table-column prop="timeRange" label="时间段" />
        <el-table-column prop="satelliteId" label="卫星号" />
        <el-table-column prop="frequency" label="频点" />
        <el-table-column prop="timeDiff" label="时差(ns)" />
        <el-table-column prop="accuracyLevel" label="授时等级" />
      </el-table>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from "vue";
import BarChart from "@/components/lineChart/lineChart.vue";
import { useHome } from "@/store/modules/home";
import apiAjax from "@/api/index";
const HomeData = useHome();
const satelliteCodingType = {
  "01": "BDS",
  "02": "GPS",
  "03": "GLONASS",
  "04": "GALILEO",
};
// 生成图表数据
const lineChartOption = ref({
  xAxis: {
    type: "category",
    data: ["BDS", "GPS", "GLONASS", "GALILEO"], // 4个导航系统
  },
  yAxis: {
    type: "value",
    name: "授时等级",
    max: 8,
    min: -1,
  },
  legend: {
    bottom: 10,
    data: ["星地融合", "BDS B2b PPP", "星地准实时"],
  },
  tooltip: {
    trigger: "axis",
    axisPointer: {
      type: "shadow",
    },
    formatter: function (params) {
      let result = `${params[0].name}<br/>`;
      params.forEach((param) => {
        // 只显示值大于等于0的数据
        if (param.value >= 0) {
          result += `${param.seriesName}: ${param.value}<br/>`;
        }
      });
      return result;
    },
  },
  series: [
    {
      name: "星地融合",
      type: "bar",
      data: [0, 0, 0, 0],
      itemStyle: {
        color: "#409EFF",
      },
    },
    {
      name: "BDS B2b PPP",
      type: "bar",
      data: [0, 0, 0, 0],
      itemStyle: {
        color: "#67C23A",
      },
    },
    {
      name: "星地准实时",
      type: "bar",
      data: [0, 0, 0, 0],
      itemStyle: {
        color: "#E6A23C",
      },
    },
  ],
});

// 生成授时精度列表数据
const accuracyList = ref([]);
const accuracyChartRef = ref();

// 处理柱状图数据
const processBarChartData = (bdsb2bList, xtwmsspcList, xtzbxwsspcList) => {
  // 初始化数据数组
  const xtwmsspcData = [nul, nul, nul, nul]; // 星地融合
  const bdsb2bData = [nul, nul, nul, nul]; // BDS B2b PPP
  const xtzbxwsspcData = [nul, nul, nul, nul]; // 星地准实时

  // 处理星地融合数据
  xtwmsspcList.forEach((item) => {
    const systemIndex = parseInt(item.navigationSystemCode) - 1;
    if (systemIndex >= 0) {
      xtwmsspcData[systemIndex] = item.accuracyLevel;
    }
  });

  // 处理BDS B2b PPP数据
  bdsb2bList.forEach((item) => {
    const systemIndex = parseInt(item.navigationSystemCode) - 1;
    if (systemIndex >= 0) {
      bdsb2bData[systemIndex] = item.accuracyLevel;
    }
  });

  // 处理星地准实时数据
  xtzbxwsspcList.forEach((item) => {
    const systemIndex = parseInt(item.navigationSystemCode) - 1;
    if (systemIndex >= 0) {
      xtzbxwsspcData[systemIndex] = item.accuracyLevel;
    }
  });

  return {
    xtwmsspcData,
    bdsb2bData,
    xtzbxwsspcData,
  };
};

// 处理表格数据
const processTableData = (wxwmsspcList) => {
  return wxwmsspcList.map((item) => ({
    timeRange: item.timestamp,
    satelliteId: item.satellitePrn || "-",
    frequency: item.frequencyId,
    timeDiff: item.std || "-",
    accuracyLevel: item.accuracyLevel,
  }));
};

const getSatelliteAccuracy = async () => {
  try {
    let { data } = await apiAjax.get(
      "/api/jnx/fusion/apps/satellite/home/<USER>/accuracyLevel/getLatest",
    );
    console.log("data", data);

    if (data) {
      // 处理柱状图数据
      const { xtwmsspcData, bdsb2bData, xtzbxwsspcData } = processBarChartData(
        data.bdsb2bpppscAccuracyLevellList || [],
        data.xtwmsspcAccuracyLevellList || [],
        data.xtzbxwsspcAccuracyLevellList || [],
      );
      console.log(
        "xtwmsspcData, bdsb2bData, xtzbxwsspcData",
        xtwmsspcData,
        bdsb2bData,
        xtzbxwsspcData,
      );
      // 更新图表数据
      lineChartOption.value.series[0].data = xtwmsspcData;
      lineChartOption.value.series[1].data = bdsb2bData;
      lineChartOption.value.series[2].data = xtzbxwsspcData;

      // 处理表格数据
      accuracyList.value = processTableData(
        data.wxwmsspcAccuracyLevelList || [],
      );

      // 更新图表
      if (accuracyChartRef.value) {
        accuracyChartRef.value.setInfos();
        accuracyChartRef.value.getIns().setOption(lineChartOption.value);
      }
    }
  } catch (error) {
    console.error("获取卫星精度数据失败:", error);
  }
};

onMounted(() => {
  HomeData.setChartfn("accuracy", getSatelliteAccuracy);
});

// 添加授时精度等级说明数据
const accuracyLevels = {
  0: "0~5ns",
  1: "5~10ns",
  2: "10~20ns",
  3: "20~30ns",
  4: "30~50ns",
  5: "50~100ns",
  6: "100~1000ns",
  7: ">1000ns",
};
</script>

<style lang="scss" scoped>
.satellite-accuracy {
  .chart-section {
    background: #fff;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
    padding-bottom: 0px;
    // margin-bottom: 20px;

    .chart-header {
      margin-bottom: 20px;

      .header-main {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 12px;

        .time-control {
          display: flex;
          align-items: center;
          gap: 8px;

          .time-range {
            font-size: 14px;
            color: #606266;
            min-width: 100px;
            text-align: center;
          }

          .el-button {
            padding: 0;
            height: 32px;
            width: 32px;
            display: flex;
            align-items: center;
            justify-content: center;

            &:not(:disabled) {
              color: #409eff;

              &:hover {
                color: #66b1ff;
                background-color: #f5f7fa;
              }
            }

            &:disabled {
              color: #c0c4cc;
            }

            .el-icon {
              font-size: 16px;
            }
          }
        }
      }

      .title-wrapper {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-bottom: 12px;

        i {
          color: #409eff;
          font-size: 18px;
        }

        .chart-title {
          font-size: 16px;
          font-weight: 600;
          color: #1f2f3d;
        }
      }

      .legend-info {
        margin: 12px 0;
        padding: 12px;
        background: #f8f9fa;
        border-radius: 4px;

        .info-title {
          color: #606266;
          font-size: 14px;
          margin-bottom: 8px;
        }

        .info-content {
          display: grid;
          grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
          gap: 8px;

          .info-item {
            display: flex;
            align-items: center;
            gap: 4px;
            font-size: 13px;

            .level {
              color: #606266;
              white-space: nowrap;
            }

            .range {
              color: #909399;
            }
          }
        }
      }

      .title-divider {
        margin-top: 8px;
        height: 2px;
        background: linear-gradient(
          90deg,
          #409eff 0%,
          rgba(64, 158, 255, 0.2) 100%
        );
        border-radius: 1px;
      }
    }

    .chart-content {
      height: 300px;
    }
  }

  .table-section {
    // margin-top: 20px;
    background: #fff;
    border-radius: 8px;
    padding: 0 20px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);

    .table-header {
      margin-bottom: 20px;

      .title-wrapper {
        display: flex;
        align-items: center;
        gap: 8px;

        i {
          color: #409eff;
          font-size: 18px;
        }

        .table-title {
          font-size: 16px;
          font-weight: 600;
          color: #1f2f3d;
        }
      }

      .title-divider {
        margin-top: 8px;
        height: 2px;
        background: linear-gradient(
          90deg,
          #409eff 0%,
          rgba(64, 158, 255, 0.2) 100%
        );
        border-radius: 1px;
      }
    }
  }
}
</style>
