<template>
  <div class="fusion-info">
    <div class="chart-content">
      <line-chart :data="lineChartOption" ref="fusionChartRef" />
    </div>
    <div class="tables-section">
      <div class="table-wrapper">
        <div class="table-title">星地融合授时偏差</div>
        <el-table :data="[]" border style="width: 100%" height="250">
          <el-table-column prop="generateTime" label="UTC-时间" width="180" />
          <el-table-column prop="BDS" label="UTC-BDS (ns)" />
          <el-table-column prop="GPS" label="UTC-GPS (ns)" />
          <el-table-column prop="GLONASS" label="UTC-GLONASS (ns)" />
          <el-table-column prop="GALILEO" label="UTC-GALILEO (ns)" />
        </el-table>
      </div>
      <div class="table-wrapper">
        <div class="table-title">星地融合授时偏差模型参数</div>
        <el-table :data="[]" border style="width: 100%" height="250">
          <el-table-column
            prop="generateTime"
            label="参数成时间(UTC)"
            width="180"
          />
          <el-table-column prop="system" label="时差类型" />
          <el-table-column prop="TOC" label="TOC" width="100" />
          <el-table-column prop="A0" label="A0(1E-10s)" />
          <el-table-column prop="A1" label="A1(1E-16s/s)" />
          <el-table-column prop="A2" label="A2(1E-21 s/s²)" />
        </el-table>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from "vue";
import LineChart from "@/components/lineChart/lineChart.vue";
import { useHome } from "@/store/modules/home";
import apiAjax from "@/api/index";
const HomeData = useHome();
// 生成图表数据
const lineChartOption = ref({
  xAxis: {
    type: "category",
    data: [],
  },
  yAxis: {
    type: "value",
    name: "时差(ns)",
  },
  legend: {
    bottom: 10,
    data: [],
  },
  grid: {
    top: "30px",
    left: "50px",
    right: "30px",
    bottom: "60px",
  },
  tooltip: {
    trigger: "axis",
    formatter: function (params) {
      const now = new Date();
      const hour = parseInt(params[0].name);
      const time = new Date(
        now.getFullYear(),
        now.getMonth(),
        now.getDate(),
        hour,
        0,
        0,
      );
      const formattedTime = time
        .toLocaleString("zh-CN", {
          year: "numeric",
          month: "2-digit",
          day: "2-digit",
          hour: "2-digit",
          minute: "2-digit",
          second: "2-digit",
          hour12: false,
        })
        .replace(/\//g, "-");

      let result = `${formattedTime}<br/>`;
      params.forEach((param) => {
        result += `${param.seriesName}: ${param.value} ns<br/>`;
      });
      return result;
    },
  },
  series: [],
});

// 生成星地融合授时偏差数据
const timeDiffList = ref([]);
const fusionChartRef = ref();
// 生成星地融合授时偏差模型参数数据
const modelParamsList = ref([]);

const satelliteCodingType = {
  "01": "BDS",
  "02": "GPS",
  "03": "GLONASS",
  "04": "GALILEO",
};

const getfusionData = async () => {
  let { data } = await apiAjax.get(
    "/api/jnx/fusion/apps/satellite/home/<USER>/timeDiff/xtwmsspc/getLatest",
  );
  console.log("data", data);
  // 这里写一个整理数据 然后 更新图形
  // fusionChartRef.value.setInfos();
  // fusionChartRef.value.getIns().setOption({});
};
onMounted(() => {
  HomeData.setChartfn("fusion", getfusionData);
});
</script>

<style lang="scss" scoped>
.fusion-info {
  .chart-content {
    height: 420px;
    margin-bottom: 20px;
  }

  .tables-section {
    display: flex;
    gap: 20px;
    margin-top: 20px;

    .table-wrapper {
      flex: 1;
      background-color: #fff;
      border-radius: 4px;
      padding: 15px;
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);

      .table-title {
        font-size: 16px;
        font-weight: 500;
        margin-bottom: 15px;
        color: #303133;
        padding-left: 5px;
        border-left: 4px solid #409eff;
      }
    }
  }
}
</style>
