<template>
  <div class="pseudo-range-info">
    <div class="chart-content">
      <line-chart :data="lineChartOption" />
    </div>
    <div class="tables-section">
      <div class="wrapper">
        <div class="table-title">卫星伪码授时偏差</div>
        <el-table :data="timeDiffList" border style="width: 100%" height="250">
          <el-table-column prop="time" label="UTC-时间" width="180" />
          <el-table-column prop="satelliteId" label="卫星号" width="100" />
          <el-table-column prop="timeDiff" label="时差(ns)" />
        </el-table>
      </div>
      <div class="table-wrapper">
        <div class="table-title">卫星伪码授时偏差模型参数</div>
        <el-table
          :data="modelParamsList"
          border
          style="width: 100%"
          height="250"
        >
          <el-table-column
            prop="generateTime"
            label="参数生成时间(UTC)"
            width="180"
          />
          <el-table-column prop="satelliteId" label="卫星号" width="100" />
          <el-table-column prop="TOC" label="TOC" width="100" />
          <el-table-column prop="A0" label="A0(1E-10s)" />
          <el-table-column prop="A1" label="A1(1E-16s/s)" />
        </el-table>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from "vue";
import LineChart from "@/components/lineChart/lineChart.vue";

const activeTab = ref("timeList");

// 生成卫星编号列表
const generateSatelliteIds = () => {
  const satellites = [];
  // BDS: C01-C63
  for (let i = 1; i <= 63; i++) {
    satellites.push(`C${String(i).padStart(2, "0")}`);
  }
  // GPS: G01-G32
  for (let i = 1; i <= 32; i++) {
    satellites.push(`G${String(i).padStart(2, "0")}`);
  }
  // GLONASS: R01-R27
  for (let i = 1; i <= 27; i++) {
    satellites.push(`R${String(i).padStart(2, "0")}`);
  }
  // GALILEO: E01-E30
  for (let i = 1; i <= 30; i++) {
    satellites.push(`E${String(i).padStart(2, "0")}`);
  }
  return satellites;
};

const satellites = generateSatelliteIds();

// 修改图表配置
const lineChartOption = ref({
  xAxis: {
    type: "category",
    data: Array.from(
      { length: 24 },
      (_, i) => `${String(i).padStart(2, "0")}:00`,
    ),
  },
  yAxis: {
    type: "value",
    name: "时差(ns)",
  },
  legend: {
    type: "scroll",
    bottom: 10,
    data: satellites.slice(0, 10),
  },
  grid: {
    top: "30px",
    left: "50px",
    right: "30px",
    bottom: "60px",
  },
  tooltip: {
    trigger: "axis",
    formatter: function (params) {
      // 获取当前日期
      const now = new Date();
      // 提取小时数
      const hour = parseInt(params[0].name.split(":")[0]);
      // 创建完整的UTC时间
      const date = new Date(
        now.getFullYear(),
        now.getMonth(),
        now.getDate(),
        hour,
        0,
        0,
      );
      const fullTime = formatUTCTime(date);

      let result = `UTC时间: ${fullTime}<br/>`;
      params.forEach((param) => {
        result += `${param.seriesName}: ${param.value} ns<br/>`;
      });
      return result;
    },
  },
  series: satellites.slice(0, 10).map((satellite) => ({
    name: satellite,
    data: Array.from({ length: 24 }, () =>
      (Math.random() * 200 - 100).toFixed(1),
    ),
    type: "line",
    showSymbol: true,
    symbolSize: 6,
    smooth: true,
    lineStyle: { width: 2 },
  })),
});

// 修改时间格式化函数
const formatUTCTime = (date) => {
  return date.toISOString().replace("T", " ").slice(0, -5);
};

// 修改数据生成函数
const timeDiffList = ref([]);
const generateTimeDiffList = () => {
  const now = new Date();
  timeDiffList.value = Array.from({ length: 20 }, (_, i) => {
    const time = new Date(now - i * 60000);
    return {
      time: formatUTCTime(time),
      satelliteId: satellites[Math.floor(Math.random() * satellites.length)],
      timeDiff: (Math.random() * 100).toFixed(0),
    };
  });
};

const modelParamsList = ref([]);
const generateModelParams = () => {
  const now = new Date();
  modelParamsList.value = satellites.slice(0, 20).map((satelliteId, i) => {
    const time = new Date(now - Math.floor(i / 4) * 600000);
    return {
      generateTime: formatUTCTime(time),
      satelliteId,
      TOC: 331200,
      A0: (Math.random() * 100).toFixed(0),
      A1: (Math.random() * 100).toFixed(0),
    };
  });
};

onMounted(() => {
  console.log(22)
  generateTimeDiffList();
  generateModelParams();
  // 每分钟更新卫星融合授时偏差
  setInterval(generateTimeDiffList, 60000);
  // 每10分钟更新卫星融合授时偏差模型参数
  setInterval(generateModelParams, 600000);
});
</script>

<style lang="scss" scoped>
.pseudo-range-info {
  .chart-content {
    height: 420px;
    margin-bottom: 20px;
  }

  .tables-section {
    display: flex;
    gap: 20px;
    margin-top: 20px;

    .table-wrapper,
    .wrapper {
      background-color: #fff;
      border-radius: 4px;
      padding: 15px;
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);

      .table-title {
        font-size: 16px;
        font-weight: 500;
        margin-bottom: 15px;
        color: #303133;
        padding-left: 5px;
        border-left: 4px solid #409eff;
      }

      .pagination-container {
        display: none;
      }
    }
    .table-wrapper {
      flex: 1;
    }
    .wrapper {
      width: 600px;
    }
  }
}
</style>
