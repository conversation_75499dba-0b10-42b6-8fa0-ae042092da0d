<template>
  <div class="pseudo-range-info">
    <div class="chart-content">
      <line-chart :data="lineChartOption" />
    </div>
    <div class="tables-section">
      <div class="wrapper">
        <div class="table-title">卫星伪码授时偏差</div>
        <el-table :data="timeDiffList" border style="width: 100%" height="250">
          <el-table-column prop="time" label="UTC-时间" width="180" />
          <el-table-column prop="satelliteId" label="卫星号" width="100" />
          <el-table-column prop="timeDiff" label="时差(ns)" />
        </el-table>
      </div>
      <div class="table-wrapper">
        <div class="table-title">卫星伪码授时偏差模型参数</div>
        <el-table
          :data="modelParamsList"
          border
          style="width: 100%"
          height="250"
        >
          <el-table-column
            prop="generateTime"
            label="参数生成时间(UTC)"
            width="180"
          />
          <el-table-column prop="satelliteId" label="卫星号" width="100" />
          <el-table-column prop="TOC" label="TOC" width="100" />
          <el-table-column prop="A0" label="A0(1E-10s)" />
          <el-table-column prop="A1" label="A1(1E-16s/s)" />
        </el-table>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from "vue";
import LineChart from "@/components/lineChart/lineChart.vue";
import { useHome } from "@/store/modules/home";
import apiAjax from "@/api/index";
const HomeData = useHome();
// 修改图表配置
const lineChartOption = ref({
  xAxis: {
    type: "category",
    data: [],
  },
  yAxis: {
    type: "value",
    name: "时差(ns)",
  },
  legend: {
    type: "scroll",
    bottom: 10,
    data: [],
  },
  grid: {
    top: "30px",
    left: "50px",
    right: "30px",
    bottom: "60px",
  },
  tooltip: {
    trigger: "axis",
    formatter: function (params) {
      const timeLabel = params[0].name;
      let result = `${timeLabel}<br/>`;
      params.forEach((param) => {
        if (param.value !== null && param.value !== undefined) {
          result += `${param.seriesName}: ${param.value} ns<br/>`;
        }
      });
      return result;
    },
  },
  series: [],
});

const modelParamsList = ref([]);

const timeDiffList = ref([]);

const getPseudoRangeInfo = async () => {
  const { data } = await apiAjax.get(
    "/api/jnx/fusion/apps/satellite/home/<USER>/timeDiff/wxwmsspc/getLatest",
  );
  console.log(data);
};
onMounted(() => {
  HomeData.setChartfn("pseudoRange", getPseudoRangeInfo);
});
</script>

<style lang="scss" scoped>
.pseudo-range-info {
  .chart-content {
    height: 420px;
    margin-bottom: 20px;
  }

  .tables-section {
    display: flex;
    gap: 20px;
    margin-top: 20px;

    .table-wrapper,
    .wrapper {
      background-color: #fff;
      border-radius: 4px;
      padding: 15px;
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);

      .table-title {
        font-size: 16px;
        font-weight: 500;
        margin-bottom: 15px;
        color: #303133;
        padding-left: 5px;
        border-left: 4px solid #409eff;
      }

      .pagination-container {
        display: none;
      }
    }
    .table-wrapper {
      flex: 1;
    }
    .wrapper {
      width: 600px;
    }
  }
}
</style>
